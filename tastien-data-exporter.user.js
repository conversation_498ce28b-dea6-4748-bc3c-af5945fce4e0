// ==UserScript==
// @name         Tastien数据导出助手
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  自动导出Tastien平台指定月份范围的业务数据
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @updateURL    
// @downloadURL  
// ==/UserScript==

/*
 * 版本: 1.0.0
 * 变更记录:
 * - 2025-01-01: 初始版本，实现基础的月份范围数据导出功能
 */

(function() {
    'use strict';

    // 全局变量
    let isProcessing = false;
    let currentMonth = 1;
    let endMonth = 12;
    let currentYear = new Date().getFullYear();

    // 创建悬浮按钮
    function createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'tastien-export-btn';
        button.innerHTML = '📊';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        
        button.addEventListener('click', showModal);
        document.body.appendChild(button);
    }

    // 创建模态框
    function showModal() {
        if (isProcessing) {
            alert('正在处理中，请稍候...');
            return;
        }

        const modal = document.createElement('div');
        modal.id = 'tastien-modal';
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10001; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 8px; width: 300px;">
                    <h3>选择导出月份范围</h3>
                    <div style="margin: 10px 0;">
                        <label>开始月份 (1-12): </label>
                        <input type="number" id="start-month" min="1" max="12" value="1" style="width: 60px;">
                    </div>
                    <div style="margin: 10px 0;">
                        <label>结束月份 (1-12): </label>
                        <input type="number" id="end-month" min="1" max="12" value="12" style="width: 60px;">
                    </div>
                    <div style="margin: 20px 0; text-align: center;">
                        <button id="start-export" style="margin-right: 10px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">开始导出</button>
                        <button id="cancel-export" style="padding: 8px 16px; background: #ccc; color: black; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('start-export').addEventListener('click', startExport);
        document.getElementById('cancel-export').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }

    // 开始导出流程
    function startExport() {
        const startMonth = parseInt(document.getElementById('start-month').value);
        const endMonth = parseInt(document.getElementById('end-month').value);

        if (startMonth < 1 || startMonth > 12 || endMonth < 1 || endMonth > 12) {
            alert('请输入有效的月份 (1-12)');
            return;
        }

        if (startMonth > endMonth) {
            alert('开始月份不能大于结束月份');
            return;
        }

        // 关闭模态框
        const modal = document.getElementById('tastien-modal');
        document.body.removeChild(modal);

        // 开始处理
        isProcessing = true;
        currentMonth = startMonth;
        endMonth = endMonth;

        console.log(`开始导出 ${startMonth} 月到 ${endMonth} 月的数据`);
        processMonth(currentMonth);
    }

    // 处理单个月份
    function processMonth(month) {
        if (month > endMonth) {
            isProcessing = false;
            alert('所有月份数据导出完成！');
            return;
        }

        console.log(`正在处理 ${month} 月数据...`);
        
        // 构建URL
        const startDate = `${currentYear}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${currentYear}-${month.toString().padStart(2, '0')}-${getLastDayOfMonth(currentYear, month)}`;
        
        const url = `https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=${startDate}&submitTime%5B1%5D=${endDate}`;
        
        // 跳转到新URL
        window.location.href = url;
    }

    // 获取月份最后一天
    function getLastDayOfMonth(year, month) {
        return new Date(year, month, 0).getDate().toString().padStart(2, '0');
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素 ${selector} 未找到`));
                } else {
                    setTimeout(check, 100);
                }
            }
            
            check();
        });
    }

    // 自动点击按钮
    async function autoClickButtons() {
        if (!isProcessing) return;

        try {
            console.log('等待搜索按钮...');
            const searchBtn = await waitForElement('button[type="submit"]:has(span:contains("搜 索"))');
            searchBtn.click();
            console.log('已点击搜索按钮');
            
            // 等待5秒
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            console.log('等待导出按钮...');
            const exportBtn = await waitForElement('button[type="button"]:has(span:contains("导出业务数据"))');
            exportBtn.click();
            console.log('已点击导出按钮');
            
            // 等待5秒后处理下一个月
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            currentMonth++;
            processMonth(currentMonth);
            
        } catch (error) {
            console.error('自动点击失败:', error);
            isProcessing = false;
            alert('自动点击失败，请检查页面元素');
        }
    }

    // 检查URL参数，如果包含时间范围则自动执行
    function checkAndAutoExecute() {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('submitTime[0]') && isProcessing) {
            // 页面加载完成后自动点击
            setTimeout(autoClickButtons, 2000);
        }
    }

    // 初始化
    function init() {
        // 创建悬浮按钮
        createFloatingButton();
        
        // 检查是否需要自动执行
        checkAndAutoExecute();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
