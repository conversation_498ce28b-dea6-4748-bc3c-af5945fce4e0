// ==UserScript==
// @name         Tastien数据导出助手
// @namespace    http://tampermonkey.net/
// @version      1.3.0
// @description  自动导出Tastien平台指定月份范围的业务数据
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @updateURL    
// @downloadURL  
// ==/UserScript==

/*
 * 版本: 1.3.0
 * 变更记录:
 * - 2025-01-01: 初始版本，实现基础的月份范围数据导出功能
 * - 2025-01-01: 修改为直接在页面日期输入框中填入日期，不再使用URL跳转
 * - 2025-01-01: 修复querySelector重写导致页面冲突的问题，简化按钮查找逻辑
 * - 2025-01-01: 添加暂停按钮功能，确保日期格式为YYYY-MM-DD
 * - 2025-01-01: 修复日期输入问题，改进React组件值设置方法，注意3个月时间跨度限制
 * - 2025-01-01: 重写日期选择逻辑，支持Ant Design日历面板点击选择日期
 */

(function() {
    'use strict';

    // 全局变量
    let isProcessing = false;
    let isPaused = false;
    let currentMonth = 1;
    let endMonth = 12;
    let currentYear = new Date().getFullYear();
    let pauseButton = null;


    // 创建悬浮按钮
    function createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'tastien-export-btn';
        button.innerHTML = '📊';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        button.addEventListener('click', showModal);
        document.body.appendChild(button);
    }

    // 创建暂停按钮
    function createPauseButton() {
        pauseButton = document.createElement('div');
        pauseButton.id = 'tastien-pause-btn';
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: ${isPaused ? '#52c41a' : '#ff4d4f'};
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            opacity: ${isProcessing ? '1' : '0.3'};
            pointer-events: ${isProcessing ? 'auto' : 'none'};
        `;

        pauseButton.addEventListener('click', togglePause);
        document.body.appendChild(pauseButton);
    }

    // 切换暂停状态
    function togglePause() {
        if (!isProcessing) return;

        isPaused = !isPaused;
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.background = isPaused ? '#52c41a' : '#ff4d4f';

        console.log(isPaused ? '已暂停导出' : '继续导出');

        if (!isPaused) {
            // 如果从暂停状态恢复，继续处理当前月份
            setTimeout(() => processMonth(currentMonth), 1000);
        }
    }

    // 更新暂停按钮状态
    function updatePauseButton() {
        if (pauseButton) {
            pauseButton.style.opacity = isProcessing ? '1' : '0.3';
            pauseButton.style.pointerEvents = isProcessing ? 'auto' : 'none';

            if (!isProcessing) {
                pauseButton.innerHTML = '⏸️';
                pauseButton.style.background = '#ff4d4f';
                isPaused = false;
            }
        }
    }

    // 创建模态框
    function showModal() {
        if (isProcessing) {
            alert('正在处理中，请稍候...');
            return;
        }

        const modal = document.createElement('div');
        modal.id = 'tastien-modal';
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10001; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 8px; width: 350px;">
                    <h3>选择导出月份范围</h3>
                    <div style="margin: 10px 0; padding: 10px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; font-size: 12px;">
                        <strong>注意:</strong> 系统限制时间跨度不能超过3个月，脚本会自动按月逐个处理
                    </div>
                    <div style="margin: 10px 0;">
                        <label>开始月份 (1-12): </label>
                        <input type="number" id="start-month" min="1" max="12" value="1" style="width: 60px;">
                    </div>
                    <div style="margin: 10px 0;">
                        <label>结束月份 (1-12): </label>
                        <input type="number" id="end-month" min="1" max="12" value="12" style="width: 60px;">
                    </div>
                    <div style="margin: 20px 0; text-align: center;">
                        <button id="start-export" style="margin-right: 10px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">开始导出</button>
                        <button id="cancel-export" style="padding: 8px 16px; background: #ccc; color: black; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('start-export').addEventListener('click', startExport);
        document.getElementById('cancel-export').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }

    // 开始导出流程
    function startExport() {
        const startMonth = parseInt(document.getElementById('start-month').value);
        const endMonthValue = parseInt(document.getElementById('end-month').value);

        if (startMonth < 1 || startMonth > 12 || endMonthValue < 1 || endMonthValue > 12) {
            alert('请输入有效的月份 (1-12)');
            return;
        }

        if (startMonth > endMonthValue) {
            alert('开始月份不能大于结束月份');
            return;
        }

        // 关闭模态框
        const modal = document.getElementById('tastien-modal');
        document.body.removeChild(modal);

        // 开始处理
        isProcessing = true;
        isPaused = false;
        currentMonth = startMonth;
        endMonth = endMonthValue;

        // 创建暂停按钮
        createPauseButton();
        updatePauseButton();

        console.log(`开始导出 ${startMonth} 月到 ${endMonthValue} 月的数据`);
        processMonth(currentMonth);
    }

    // 处理单个月份
    async function processMonth(month) {
        // 检查是否暂停
        if (isPaused) {
            console.log('导出已暂停');
            return;
        }

        if (month > endMonth) {
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('所有月份数据导出完成！');
            return;
        }

        console.log(`正在处理 ${month} 月数据...`);

        // 构建日期 - 确保格式为 YYYY-MM-DD
        const startDate = `${currentYear}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${currentYear}-${month.toString().padStart(2, '0')}-${getLastDayOfMonth(currentYear, month)}`;

        try {
            // 填入日期到输入框
            await fillDateInputs(startDate, endDate);

            // 等待2秒后自动点击按钮
            setTimeout(autoClickButtons, 2000);

        } catch (error) {
            console.error('处理月份失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert(`处理 ${month} 月数据时出错: ${error.message}`);
        }
    }

    // 获取月份最后一天
    function getLastDayOfMonth(year, month) {
        return new Date(year, month, 0).getDate().toString().padStart(2, '0');
    }



    // 解析日期字符串
    function parseDate(dateStr) {
        const [year, month, day] = dateStr.split('-').map(Number);
        return { year, month, day };
    }

    // 点击日历中的日期
    async function clickCalendarDate(year, month, day) {
        // 等待日历面板出现
        await waitForElement('.ant-picker-dropdown');

        // 查找对应的日期单元格
        const dateCells = document.querySelectorAll('.ant-picker-cell');

        for (let cell of dateCells) {
            const cellTitle = cell.getAttribute('title');
            if (cellTitle && cellTitle.includes(`${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`)) {
                console.log(`点击日期: ${cellTitle}`);
                cell.click();
                return true;
            }
        }

        // 如果没找到，尝试查找包含日期数字的单元格
        for (let cell of dateCells) {
            const cellText = cell.textContent.trim();
            if (cellText === day.toString() && !cell.classList.contains('ant-picker-cell-disabled')) {
                console.log(`点击日期单元格: ${day}`);
                cell.click();
                return true;
            }
        }

        return false;
    }

    // 导航到指定月份
    async function navigateToMonth(targetYear, targetMonth) {
        let attempts = 0;
        const maxAttempts = 12;

        while (attempts < maxAttempts) {
            // 查找当前显示的月份
            const monthHeaders = document.querySelectorAll('.ant-picker-header-view');
            if (monthHeaders.length === 0) {
                await new Promise(resolve => setTimeout(resolve, 500));
                attempts++;
                continue;
            }

            const currentMonthText = monthHeaders[0].textContent;
            const currentYear = parseInt(currentMonthText.match(/(\d{4})/)?.[1] || '2025');
            const currentMonth = parseInt(currentMonthText.match(/(\d+)月/)?.[1] || '1');

            console.log(`当前显示: ${currentYear}年 ${currentMonth}月, 目标: ${targetYear}年 ${targetMonth}月`);

            if (currentYear === targetYear && currentMonth === targetMonth) {
                console.log('已到达目标月份');
                return true;
            }

            // 决定点击前进还是后退
            const needGoForward = (targetYear > currentYear) || (targetYear === currentYear && targetMonth > currentMonth);

            if (needGoForward) {
                const nextBtn = document.querySelector('.ant-picker-header-next-btn');
                if (nextBtn) {
                    nextBtn.click();
                    console.log('点击下一月');
                }
            } else {
                const prevBtn = document.querySelector('.ant-picker-header-prev-btn');
                if (prevBtn) {
                    prevBtn.click();
                    console.log('点击上一月');
                }
            }

            await new Promise(resolve => setTimeout(resolve, 300));
            attempts++;
        }

        return false;
    }

    // 填入日期到输入框 - 改进版本
    async function fillDateInputs(startDate, endDate) {
        console.log(`填入日期: ${startDate} 到 ${endDate}`);

        try {
            // 等待日期输入框出现
            const startInput = await waitForElement('input[date-range="start"]');
            const endInput = await waitForElement('input[date-range="end"]');

            console.log('找到日期输入框');

            // 解析开始日期和结束日期
            const startDateObj = parseDate(startDate);
            const endDateObj = parseDate(endDate);

            // 清空现有日期
            console.log('清空现有日期...');
            startInput.value = '';
            endInput.value = '';

            // 点击开始日期输入框打开日历
            console.log('点击开始日期输入框...');
            startInput.click();
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 设置开始日期
            console.log('设置开始日期...');
            if (await navigateToMonth(startDateObj.year, startDateObj.month)) {
                await new Promise(resolve => setTimeout(resolve, 800));
                if (await clickCalendarDate(startDateObj.year, startDateObj.month, startDateObj.day)) {
                    console.log('开始日期设置成功');

                    // 验证开始日期是否正确设置
                    await new Promise(resolve => setTimeout(resolve, 500));
                    console.log(`开始日期当前值: "${startInput.value}"`);

                    // 如果是同一个月，直接设置结束日期
                    if (startDateObj.year === endDateObj.year && startDateObj.month === endDateObj.month) {
                        console.log('同月份，直接设置结束日期...');
                        await new Promise(resolve => setTimeout(resolve, 500));
                        if (await clickCalendarDate(endDateObj.year, endDateObj.month, endDateObj.day)) {
                            console.log('结束日期设置成功');
                        } else {
                            console.error('结束日期点击失败');
                        }
                    } else {
                        // 不同月份，需要导航到结束日期月份
                        console.log('不同月份，导航到结束日期月份...');
                        await new Promise(resolve => setTimeout(resolve, 800));
                        if (await navigateToMonth(endDateObj.year, endDateObj.month)) {
                            await new Promise(resolve => setTimeout(resolve, 800));
                            if (await clickCalendarDate(endDateObj.year, endDateObj.month, endDateObj.day)) {
                                console.log('结束日期设置成功');
                            } else {
                                console.error('结束日期点击失败');
                            }
                        }
                    }
                } else {
                    console.error('开始日期点击失败');
                }
            }

            // 等待日历关闭
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 验证最终结果
            console.log(`最终开始日期值: "${startInput.value}"`);
            console.log(`最终结束日期值: "${endInput.value}"`);

            // 如果日期没有正确设置，尝试点击页面其他地方关闭日历
            if (!startInput.value || !endInput.value) {
                console.log('日期设置可能失败，尝试关闭日历...');
                document.body.click();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log('日期填入完成');

        } catch (error) {
            console.error('填入日期失败:', error);
            throw error;
        }
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素 ${selector} 未找到`));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    // 查找包含特定文本的按钮
    function findButtonByText(text, buttonType = null) {
        const buttons = buttonType ?
            document.querySelectorAll(`button[type="${buttonType}"]`) :
            document.querySelectorAll('button');

        for (let btn of buttons) {
            if (btn.textContent.includes(text)) {
                return btn;
            }
        }
        return null;
    }

    // 自动点击按钮
    async function autoClickButtons() {
        if (!isProcessing || isPaused) return;

        try {
            console.log('等待搜索按钮...');
            // 查找搜索按钮
            let searchBtn = null;
            let attempts = 0;
            while (!searchBtn && attempts < 50 && !isPaused) {
                searchBtn = findButtonByText('搜 索', 'submit');
                if (!searchBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!searchBtn) {
                throw new Error('搜索按钮未找到');
            }

            searchBtn.click();
            console.log('已点击搜索按钮');

            // 等待5秒，期间检查暂停状态
            for (let i = 0; i < 25; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log('等待导出按钮...');
            // 查找导出按钮
            let exportBtn = null;
            attempts = 0;
            while (!exportBtn && attempts < 50 && !isPaused) {
                exportBtn = findButtonByText('导出业务数据', 'button');
                if (!exportBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!exportBtn) {
                throw new Error('导出按钮未找到');
            }

            exportBtn.click();
            console.log('已点击导出按钮');

            // 等待5秒后处理下一个月，期间检查暂停状态
            for (let i = 0; i < 25; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            if (!isPaused) {
                currentMonth++;
                processMonth(currentMonth);
            }

        } catch (error) {
            console.error('自动点击失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('自动点击失败，请检查页面元素');
        }
    }

    // 初始化
    function init() {
        // 创建悬浮按钮
        createFloatingButton();

        // 创建暂停按钮（初始隐藏）
        createPauseButton();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
